# برنامج المحاسبة الإداري
Write-Host "========================================" -ForegroundColor Green
Write-Host "    برنامج المحاسبة الإداري" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "معلومات الدخول:" -ForegroundColor Cyan
Write-Host "اسم المستخدم: admin" -ForegroundColor White
Write-Host "كلمة المرور: admin" -ForegroundColor White
Write-Host ""
Write-Host "تشغيل البرنامج..." -ForegroundColor Green

# تشغيل البرنامج مباشرة
try {
    & ".\venv\Scripts\python.exe" ".\main_silent.py"
} catch {
    Write-Host "خطأ في تشغيل البرنامج: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "جرب تشغيل الأمر التالي يدوياً:" -ForegroundColor Yellow
    Write-Host '& "venv\Scripts\python.exe" main_silent.py' -ForegroundColor Cyan
}

Write-Host ""
Write-Host "تم إغلاق البرنامج." -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج"
