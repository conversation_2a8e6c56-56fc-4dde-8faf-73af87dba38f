from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter

from database import Employee
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class EmployeesWidget(QWidget):
    """واجهة إدارة العمال"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم العمال: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم العمال: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعملاء
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للعملاء
        title_label = QLabel("👷‍♂️ إدارة العمال المتطورة - نظام شامل ومتقدم لإدارة العمال مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للعملاء
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch()

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch()

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للعملاء
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        search_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-weight: 900;
                max-height: 38px;
                min-height: 34px;
                margin: 0px;
            }
        """)

        # حقل البحث المطور والمحسن مطابق للعملاء
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم العامل، الهاتف، البريد الإلكتروني أو المنصب...")
        self.search_edit.textChanged.connect(self.filter_employees)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # زر البحث المطور مع تأثيرات بصرية
        search_button = QPushButton("🔍 بحث")
        search_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_button.clicked.connect(self.filter_employees)
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: white;
                border: 3px solid #1e40af;
                border-radius: 15px;
                padding: 8px 20px;
                font-weight: bold;
                max-height: 38px;
                min-height: 34px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.5 #1d4ed8, stop:1 #1e3a8a);
                border: 3px solid #1e3a8a;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1d4ed8, stop:0.5 #1e3a8a, stop:1 #1e40af);
                transform: translateY(1px);
            }
        """)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("📊 التصفية:")
        filter_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-weight: 900;
                max-height: 38px;
                min-height: 34px;
                margin: 0px;
            }
        """)

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول العمال المتطور والمحسن
        self.create_advanced_employees_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.employees_table, 1)  # إعطاء الجدول أولوية في التمدد

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

        # تحميل البيانات
        self.refresh_data()

        # إنشاء بيانات تجريبية إذا لم توجد
        self.create_sample_data_if_empty()

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 2px;
                max-height: 38px;
                min-height: 34px;
                min-width: 150px;
            }
            QFrame:hover {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
            }
        """)

        # تخطيط أفقي للإطار
        frame_layout = QHBoxLayout()
        frame_layout.setContentsMargins(8, 4, 8, 4)
        frame_layout.setSpacing(4)

        # النص الحالي
        self.current_filter_label = QLabel("🔄 جميع العمال")
        self.current_filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 4px 8px;
                font-weight: 900;
            }
        """)

        # إضافة العناصر للتخطيط - النص في المنتصف
        frame_layout.addWidget(self.current_filter_label, 1, Qt.AlignCenter)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الإطار بالقائمة
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.setCursor(Qt.PointingHandCursor)

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(frame_layout)

    def filter_employees(self):
        """تصفية العمال بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Employee)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Employee.name.like(f"%{search_text}%") |
                    Employee.phone.like(f"%{search_text}%") |
                    Employee.email.like(f"%{search_text}%") |
                    Employee.position.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Employee.balance > 0)
            elif status == "normal":
                query = query.filter(Employee.balance == 0)
            elif status == "debtor":
                query = query.filter(Employee.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            employees = query.order_by(Employee.id.asc()).all()

            # تحديث الجدول
            self.populate_table(employees)

        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")

    def create_advanced_employees_table(self):
        """إنشاء جدول العمال المتطور والنظيف"""
        # إنشاء الجدول
        self.employees_table = QTableWidget()
        self.employees_table.setColumnCount(8)

        # عناوين الأعمدة مع الأيقونات
        headers = [
            "🆔 الرقم التسلسلي",
            "👷‍♂️ اسم العامل",
            "💼 المنصب",
            "📍 العنوان",
            "📧 البريد الإلكتروني",
            "📞 رقم الهاتف",
            "💰 الرصيد",
            "🎯 حالة العامل"
        ]

        self.employees_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.employees_table.setAlternatingRowColors(True)
        self.employees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.employees_table.setSelectionMode(QTableWidget.SingleSelection)
        self.employees_table.setSortingEnabled(True)
        self.employees_table.setShowGrid(True)

        # إعدادات الصفوف والأعمدة
        self.employees_table.verticalHeader().setVisible(False)
        self.employees_table.horizontalHeader().setStretchLastSection(False)
        self.employees_table.setWordWrap(True)
        self.employees_table.setTextElideMode(Qt.ElideRight)

        # إعداد عرض الأعمدة
        header = self.employees_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تحديد عرض محدد للأعمدة
        header.resizeSection(0, 200)  # الرقم التسلسلي - 200px
        header.resizeSection(1, 300)  # الاسم - 300px
        header.resizeSection(2, 250)  # المنصب - 250px
        header.resizeSection(3, 300)  # العنوان - 300px
        header.resizeSection(4, 300)  # البريد - 300px
        header.resizeSection(5, 200)  # الهاتف - 200px
        header.resizeSection(6, 200)  # الرصيد - 200px
        header.resizeSection(7, 200)  # الحالة - 200px

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع العمال من قاعدة البيانات (من الأقدم للأحدث)
            employees = self.session.query(Employee).order_by(Employee.id.asc()).all()
            self.populate_table(employees)
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def populate_table(self, employees):
        """ملء الجدول بالبيانات"""
        try:
            self.employees_table.setRowCount(len(employees))

            for row, employee in enumerate(employees):
                # الرقم التسلسلي مع أيقونة
                id_item = QTableWidgetItem(f"🆔 {employee.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                self.employees_table.setItem(row, 0, id_item)

                # اسم العامل مع أيقونة
                name_text = employee.name if employee.name and employee.name.strip() else "No data"
                name_item = QTableWidgetItem(f"👷‍♂️ {name_text}")
                name_item.setTextAlignment(Qt.AlignCenter)
                if name_text == "No data":
                    name_item.setForeground(QColor("#ef4444"))  # أحمر
                self.employees_table.setItem(row, 1, name_item)

                # المنصب مع أيقونة
                position_text = employee.position if employee.position and employee.position.strip() else "No data"
                position_item = QTableWidgetItem(f"💼 {position_text}")
                position_item.setTextAlignment(Qt.AlignCenter)
                if position_text == "No data":
                    position_item.setForeground(QColor("#ef4444"))  # أحمر
                self.employees_table.setItem(row, 2, position_item)

                # العنوان مع أيقونة
                address_text = employee.address if employee.address and employee.address.strip() else "No data"
                address_item = QTableWidgetItem(f"📍 {address_text}")
                address_item.setTextAlignment(Qt.AlignCenter)
                if address_text == "No data":
                    address_item.setForeground(QColor("#ef4444"))  # أحمر
                self.employees_table.setItem(row, 3, address_item)

                # البريد الإلكتروني مع أيقونة
                email_text = employee.email if employee.email and employee.email.strip() else "No data"
                email_item = QTableWidgetItem(f"📧 {email_text}")
                email_item.setTextAlignment(Qt.AlignCenter)
                if email_text == "No data":
                    email_item.setForeground(QColor("#ef4444"))  # أحمر
                self.employees_table.setItem(row, 4, email_item)

                # رقم الهاتف مع أيقونة
                phone_text = employee.phone if employee.phone and employee.phone.strip() else "No data"
                phone_item = QTableWidgetItem(f"📞 {phone_text}")
                phone_item.setTextAlignment(Qt.AlignCenter)
                if phone_text == "No data":
                    phone_item.setForeground(QColor("#ef4444"))  # أحمر
                self.employees_table.setItem(row, 5, phone_item)

                # الرصيد مع أيقونة
                balance_item = QTableWidgetItem(f"💰 {format_currency(employee.balance or 0)}")
                balance_item.setTextAlignment(Qt.AlignCenter)
                self.employees_table.setItem(row, 6, balance_item)

                # حالة العامل (الأيقونة موجودة في النص)
                status_item = QTableWidgetItem(self.get_employee_status(employee.balance or 0))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.employees_table.setItem(row, 7, status_item)

        except Exception as e:
            print(f"خطأ في ملء الجدول: {str(e)}")

    def get_employee_status(self, balance):
        """تحديد حالة العامل بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود عمال
            existing_employees = self.session.query(Employee).count()

            if existing_employees == 0:
                # إنشاء عمال تجريبيين
                sample_employees = [
                    Employee(
                        name='أحمد محمد البناء',
                        position='مهندس مدني',
                        phone='0501234567',
                        email='<EMAIL>',
                        address='الرياض - حي النخيل - شارع الملك فهد',
                        balance=8000.0,
                        salary=5000.0,
                        notes='مهندس خبير - أداء ممتاز'
                    ),
                    Employee(
                        name='فاطمة علي الكهربائية',
                        position='فني كهرباء',
                        phone='0509876543',
                        email='<EMAIL>',
                        address='جدة - حي الصفا - طريق الأمير سلطان',
                        balance=3500.0,
                        salary=3000.0,
                        notes='فني ماهر - دقيق في العمل'
                    ),
                    Employee(
                        name='محمد سالم النجار',
                        position='نجار',
                        phone='0551122334',
                        email='<EMAIL>',
                        address='الدمام - حي الفيصلية - شارع الخليج',
                        balance=-1000.0,
                        salary=2500.0,
                        notes='نجار ماهر - يحتاج متابعة مالية'
                    )
                ]

                for employee in sample_employees:
                    self.session.add(employee)

                self.session.commit()
                print(f"تم إنشاء {len(sample_employees)} عامل تجريبي")

                # تحديث الجدول
                self.refresh_data()

        except Exception as e:
            print(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
            }
            QMenu::item {
                background: transparent;
                padding: 8px 20px;
                border-radius: 8px;
                margin: 2px;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: white;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("🔄 جميع العمال", None),
            ("🟢 عمال نشطين", "active"),
            ("🟡 عمال عاديين", "normal"),
            ("🔴 عمال مدينين", "debtor")
        ]

        for text, value in filter_options:
            action = QAction(text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_employees()
