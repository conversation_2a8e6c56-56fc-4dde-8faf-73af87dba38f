from PyQt5.QtWidgets import (QW<PERSON>t, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter

from database import Supplier
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم الموردين: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم الموردين: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعملاء
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للعملاء
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للعملاء
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch()

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch()

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # حقل البحث المطور والمحسن للموردين - مختلف عن العملاء
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🚛 البحث في قاعدة بيانات الموردين - اسم الشركة، رقم الاتصال، البريد أو الموقع...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # زر البحث المطور مع تأثيرات بصرية مطابق للفواتير
        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم في الموردين")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إضافة رسالة مؤقتة بدلاً من الجدول
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 20px;
            }
        """)

        content_layout = QVBoxLayout()
        content_layout.setAlignment(Qt.AlignCenter)

        message_label = QLabel("🚛 قسم الموردين - العنوان وجزء البحث جاهزان")
        message_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 40px;
            }
        """)

        sub_message = QLabel("تم تطبيق التصميم الموحد بنجاح\nسيتم إضافة الجدول والوظائف الأخرى لاحقاً")
        sub_message.setFont(QFont("Segoe UI", 16))
        sub_message.setAlignment(Qt.AlignCenter)
        sub_message.setStyleSheet("""
            QLabel {
                color: #6b7280;
                background: transparent;
                border: none;
                padding: 20px;
            }
        """)

        content_layout.addWidget(message_label)
        content_layout.addWidget(sub_message)
        content_frame.setLayout(content_layout)

        main_layout.addWidget(top_frame)
        main_layout.addWidget(content_frame, 1)  # إعطاء المحتوى أولوية في التمدد

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 2px;
                max-height: 38px;
                min-height: 34px;
                min-width: 150px;
            }
            QFrame:hover {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
            }
        """)

        # تخطيط أفقي للإطار
        frame_layout = QHBoxLayout()
        frame_layout.setContentsMargins(8, 4, 8, 4)
        frame_layout.setSpacing(4)

        # النص الحالي
        self.current_filter_label = QLabel("🔄 جميع الموردين")
        self.current_filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 4px 8px;
                font-weight: 900;
            }
        """)

        # إضافة العناصر للتخطيط - النص في المنتصف
        frame_layout.addWidget(self.current_filter_label, 1, Qt.AlignCenter)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الإطار بالقائمة
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.setCursor(Qt.PointingHandCursor)

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(frame_layout)

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول
            self.populate_table(suppliers)

        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")

    def create_advanced_suppliers_table(self):
        """إنشاء جدول الموردين المتطور والنظيف"""
        # إنشاء الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(7)

        # عناوين الأعمدة مع الأيقونات (الترتيب الجديد)
        headers = [
            "🆔 الرقم التسلسلي",
            "🚛 اسم المورد",
            "📍 العنوان",
            "📧 البريد الإلكتروني",
            "📞 رقم الهاتف",
            "💰 الرصيد",
            "🎯 حالة المورد"
        ]

        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setSortingEnabled(True)
        self.suppliers_table.setShowGrid(True)

        # إعدادات الصفوف والأعمدة
        self.suppliers_table.verticalHeader().setVisible(False)
        self.suppliers_table.horizontalHeader().setStretchLastSection(False)
        self.suppliers_table.setWordWrap(True)
        self.suppliers_table.setTextElideMode(Qt.ElideRight)

        # إعداد عرض الأعمدة (الترتيب الجديد: رقم، اسم، عنوان، بريد، هاتف، رصيد، حالة)
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تحديد عرض محدد للأعمدة
        header.resizeSection(0, 220)  # الرقم التسلسلي - 220px
        header.resizeSection(1, 350)  # الاسم - 350px
        header.resizeSection(2, 350)  # العنوان - 350px
        header.resizeSection(3, 350)  # البريد - 350px
        header.resizeSection(4, 210)  # الهاتف - 210px
        header.resizeSection(5, 210)  # الرصيد - 210px
        header.resizeSection(6, 210)  # الحالة - 210px

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def populate_table(self, suppliers):
        """ملء الجدول بالبيانات"""
        try:
            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                # الرقم التسلسلي مع أيقونة
                id_item = QTableWidgetItem(f"🆔 {supplier.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                self.suppliers_table.setItem(row, 0, id_item)

                # اسم المورد مع أيقونة
                name_text = supplier.name if supplier.name and supplier.name.strip() else "No data"
                name_item = QTableWidgetItem(f"🚛 {name_text}")
                name_item.setTextAlignment(Qt.AlignCenter)
                if name_text == "No data":
                    name_item.setForeground(QColor("#ef4444"))  # أحمر
                self.suppliers_table.setItem(row, 1, name_item)

                # العنوان مع أيقونة (العمود الثالث)
                address_text = supplier.address if supplier.address and supplier.address.strip() else "No data"
                address_item = QTableWidgetItem(f"📍 {address_text}")
                address_item.setTextAlignment(Qt.AlignCenter)
                if address_text == "No data":
                    address_item.setForeground(QColor("#ef4444"))  # أحمر
                self.suppliers_table.setItem(row, 2, address_item)

                # البريد الإلكتروني مع أيقونة (العمود الرابع)
                email_text = supplier.email if supplier.email and supplier.email.strip() else "No data"
                email_item = QTableWidgetItem(f"📧 {email_text}")
                email_item.setTextAlignment(Qt.AlignCenter)
                if email_text == "No data":
                    email_item.setForeground(QColor("#ef4444"))  # أحمر
                self.suppliers_table.setItem(row, 3, email_item)

                # رقم الهاتف مع أيقونة (العمود الخامس)
                phone_text = supplier.phone if supplier.phone and supplier.phone.strip() else "No data"
                phone_item = QTableWidgetItem(f"📞 {phone_text}")
                phone_item.setTextAlignment(Qt.AlignCenter)
                if phone_text == "No data":
                    phone_item.setForeground(QColor("#ef4444"))  # أحمر
                self.suppliers_table.setItem(row, 4, phone_item)

                # الرصيد مع أيقونة
                balance_item = QTableWidgetItem(f"💰 {format_currency(supplier.balance or 0)}")
                balance_item.setTextAlignment(Qt.AlignCenter)
                self.suppliers_table.setItem(row, 5, balance_item)

                # حالة المورد (الأيقونة موجودة في النص)
                status_item = QTableWidgetItem(self.get_supplier_status(supplier.balance or 0))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.suppliers_table.setItem(row, 6, status_item)

        except Exception as e:
            print(f"خطأ في ملء الجدول: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود موردين
            existing_suppliers = self.session.query(Supplier).count()

            if existing_suppliers == 0:
                # إنشاء موردين تجريبيين
                sample_suppliers = [
                    Supplier(
                        name='شركة المواد الإنشائية المتحدة',
                        phone='0501234567',
                        email='<EMAIL>',
                        address='الرياض - حي الصناعية - شارع الملك فهد',
                        balance=25000.0,
                        notes='مورد رئيسي - جودة عالية'
                    ),
                    Supplier(
                        name='مؤسسة الدهانات والأصباغ',
                        phone='0509876543',
                        email='<EMAIL>',
                        address='جدة - حي الصفا - طريق الأمير سلطان',
                        balance=12000.0,
                        notes='مورد دهانات موثوق'
                    ),
                    Supplier(
                        name='شركة الأخشاب والنجارة',
                        phone='0551122334',
                        email='<EMAIL>',
                        address='الدمام - حي الفيصلية - شارع الخليج',
                        balance=-5000.0,
                        notes='مورد أخشاب - يحتاج متابعة'
                    )
                ]

                for supplier in sample_suppliers:
                    self.session.add(supplier)

                self.session.commit()
                print(f"تم إنشاء {len(sample_suppliers)} مورد تجريبي")

                # تحديث الجدول
                self.refresh_data()

        except Exception as e:
            print(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
            }
            QMenu::item {
                background: transparent;
                padding: 8px 20px;
                border-radius: 8px;
                margin: 2px;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: white;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("🔄 جميع الموردين", None),
            ("🟢 موردين نشطين", "active"),
            ("🟡 موردين عاديين", "normal"),
            ("🔴 موردين مدينين", "debtor")
        ]

        for text, value in filter_options:
            action = QAction(text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_suppliers()
