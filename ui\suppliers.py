from PyQt5.QtWidgets import (QW<PERSON>t, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter

from database import Supplier
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم الموردين: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم الموردين: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعملاء
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للعملاء
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للعملاء
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch()

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch()

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للعملاء
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        search_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-weight: 900;
                max-height: 38px;
                min-height: 34px;
                margin: 0px;
            }
        """)

        # حقل البحث المطور والمحسن مطابق للعملاء
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم المورد، الهاتف، البريد الإلكتروني أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # زر البحث المطور مع تأثيرات بصرية
        search_button = QPushButton("🔍 بحث")
        search_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8);
                color: white;
                border: 3px solid #1e40af;
                border-radius: 15px;
                padding: 8px 20px;
                font-weight: bold;
                max-height: 38px;
                min-height: 34px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.5 #1d4ed8, stop:1 #1e3a8a);
                border: 3px solid #1e3a8a;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1d4ed8, stop:0.5 #1e3a8a, stop:1 #1e40af);
                transform: translateY(1px);
            }
        """)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("📊 التصفية:")
        filter_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-weight: 900;
                max-height: 38px;
                min-height: 34px;
                margin: 0px;
            }
        """)

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الموردين المتطور والمحسن
        self.create_advanced_suppliers_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.suppliers_table, 1)  # إعطاء الجدول أولوية في التمدد

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

        # تحميل البيانات
        self.refresh_data()

        # إنشاء بيانات تجريبية إذا لم توجد
        self.create_sample_data_if_empty()

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 2px;
                max-height: 38px;
                min-height: 34px;
                min-width: 150px;
            }
            QFrame:hover {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
            }
        """)

        # تخطيط أفقي للإطار
        frame_layout = QHBoxLayout()
        frame_layout.setContentsMargins(8, 4, 8, 4)
        frame_layout.setSpacing(4)

        # النص الحالي
        self.current_filter_label = QLabel("🔄 جميع الموردين")
        self.current_filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                background: transparent;
                border: none;
                padding: 4px 8px;
                font-weight: 900;
            }
        """)

        # إضافة العناصر للتخطيط - النص في المنتصف
        frame_layout.addWidget(self.current_filter_label, 1, Qt.AlignCenter)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الإطار بالقائمة
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.setCursor(Qt.PointingHandCursor)

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(frame_layout)

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass

    def create_advanced_suppliers_table(self):
        """إنشاء جدول الموردين المتطور والنظيف"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        # سيتم إضافة هذه الوظيفة لاحقاً
        pass
