from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QSizePolicy, QFrame, QMenu, QAction,
                            QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QMessageBox, QApplication, QDialog, QFormLayout, QComboBox,
                            QDateEdit, QDoubleSpinBox, QGridLayout, QShortcut)
from PyQt5.QtCore import Qt, QTimer, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPainter, QKeySequence, QCursor

from database import Supplier
from utils import format_currency
from ui.unified_styles import UnifiedStyles

class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, session):
        super().__init__()
        try:
            self.session = session
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة قسم الموردين: {str(e)}")
            # إنشاء واجهة بسيطة في حالة الخطأ
            from PyQt5.QtWidgets import QVBoxLayout, QLabel
            layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تحميل قسم الموردين: {str(e)}")
            layout.addWidget(error_label)
            self.setLayout(layout)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعملاء
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للعملاء
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للعملاء
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(8, 8, 8, 8)
        search_layout.setSpacing(8)

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(0, 0, 0, 0)
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch()

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch()

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # حقل البحث المطور والمحسن للموردين - مختلف عن العملاء
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🚛 البحث في قاعدة بيانات الموردين - اسم الشركة، رقم الاتصال، البريد أو الموقع...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            QLineEdit:focus {
                border: 3px solid rgba(59, 130, 246, 1.0);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.2 rgba(248, 250, 252, 1.0),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 1.0),
                    stop:0.8 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(226, 232, 240, 0.9));
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(107, 114, 128, 0.8);
                font-style: italic;
            }
        """)

        # زر البحث المطور مع تأثيرات بصرية مطابق للفواتير
        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم في الموردين")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إضافة رسالة مؤقتة بدلاً من الجدول
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 20px;
            }
        """)

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(5)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 📊 إنشاء وإضافة الجدول المتطور                                         │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.create_advanced_suppliers_table()
        content_layout.addWidget(self.suppliers_table)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 🔄 تحميل البيانات الأولية                                              │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.refresh_data()

        content_frame.setLayout(content_layout)

        main_layout.addWidget(top_frame)
        main_layout.addWidget(content_frame, 1)  # إعطاء المحتوى أولوية في التمدد

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow)
        filter_layout.addWidget(self.current_filter_label, 1)  # يأخذ المساحة المتبقية
        filter_layout.addWidget(self.filter_menu_button)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.left_arrow.clicked.connect(self.show_filter_menu)
        self.filter_menu_button.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame
        self.status_filter_frame.setLayout(filter_layout)

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول
            self.populate_table(suppliers)

        except Exception as e:
            print(f"خطأ في تصفية البيانات: {str(e)}")

    def create_advanced_suppliers_table(self):
        """
        ═══════════════════════════════════════════════════════════════════════════════
        🚛 إنشاء جدول الموردين المتطور والنظيف - مطابق للعملاء
        ═══════════════════════════════════════════════════════════════════════════════

        المميزات:
        • 9 أعمدة مع أيقونات موحدة
        • مقاسات محسنة ومتوازنة
        • تصميم احترافي مع تأثيرات بصرية
        • قابل للترتيب والتحديد المتعدد
        • علامة مائية "Smart Finish"
        """
        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 📊 إنشاء الجدول الأساسي                                                │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(9)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 🏷️ عناوين الأعمدة مع الأيقونات الموحدة للموردين                      │
        # └─────────────────────────────────────────────────────────────────────────┘
        headers = [
            "🔢 ID",                    # العمود 0 - 100px
            "🚛 اسم المورد",            # العمود 1 - 300px
            "🏭 العنوان",               # العمود 2 - 300px
            "📧 البريد الإلكتروني",      # العمود 3 - 240px
            "📱 رقم الهاتف",            # العمود 4 - 170px
            "💵 الرصيد",               # العمود 5 - 160px
            "⭐ حالة المورد",           # العمود 6 - 170px
            "📋 الملاحظات",            # العمود 7 - 280px
            "🗓️ تاريخ الإضافة"          # العمود 8 - 180px
        ]
        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ ⚙️ إعدادات الجدول الأساسية                                             │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.ExtendedSelection)  # تحديد متعدد مع Ctrl/Shift
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setAlternatingRowColors(False)
        self.suppliers_table.setSortingEnabled(True)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 📏 إعدادات الصفوف والأعمدة                                             │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.suppliers_table.verticalHeader().setDefaultSectionSize(50)
        self.suppliers_table.verticalHeader().setVisible(False)

        header = self.suppliers_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 🔧 إعداد نمط تغيير حجم الأعمدة                                          │
        # └─────────────────────────────────────────────────────────────────────────┘
        for i in range(9):
            header.setSectionResizeMode(i, QHeaderView.Interactive)

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 📐 مقاسات الأعمدة المحسنة والمتوازنة                                   │
        # └─────────────────────────────────────────────────────────────────────────┘
        # إجمالي العرض: 1,900px
        header.resizeSection(0, 100)  # 🔢 ID - 100px
        header.resizeSection(1, 300)  # 🚛 اسم المورد - 300px
        header.resizeSection(2, 300)  # 🏭 العنوان - 300px
        header.resizeSection(3, 240)  # 📧 البريد الإلكتروني - 240px
        header.resizeSection(4, 170)  # 📱 رقم الهاتف - 170px
        header.resizeSection(5, 160)  # 💵 الرصيد - 160px
        header.resizeSection(6, 170)  # ⭐ حالة المورد - 170px
        header.resizeSection(7, 280)  # 📋 الملاحظات - 280px
        header.resizeSection(8, 180)  # 🗓️ تاريخ الإضافة - 180px

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 🎯 تحسينات التفاعل المتقدمة                                            │
        # └─────────────────────────────────────────────────────────────────────────┘
        # النقر المزدوج للتعديل السريع
        self.suppliers_table.doubleClicked.connect(self.quick_edit_supplier)

        # قائمة السياق (Right-click menu) مع خيارات سريعة
        self.suppliers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.suppliers_table.customContextMenuRequested.connect(self.show_context_menu)

        # اختصارات لوحة المفاتيح المتقدمة
        self.setup_keyboard_shortcuts()

        # ┌─────────────────────────────────────────────────────────────────────────┐
        # │ 🎨 تطبيق التصميم والتأثيرات                                            │
        # └─────────────────────────────────────────────────────────────────────────┘
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def populate_table(self, suppliers):
        """
        ═══════════════════════════════════════════════════════════════════════════════
        📊 ملء جدول الموردين بالبيانات مع التحسينات المتطورة
        ═══════════════════════════════════════════════════════════════════════════════
        """
        try:
            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 🆔 العمود 0: ID مع أيقونات متطورة وتلوين حسب الحالة المالية       │
                # └─────────────────────────────────────────────────────────────────────┘
                balance_value = supplier.balance or 0

                # أيقونات متطورة حسب المستوى المالي
                if balance_value >= 10000:
                    id_icon = "👑"  # مورد ملكي - رصيد عالي جداً
                    color = QColor(147, 51, 234)  # #9333ea - بنفسجي ملكي
                elif balance_value >= 5000:
                    id_icon = "💎"  # مورد VIP - رصيد عالي
                    color = QColor(168, 85, 247)  # #a855f7 - بنفسجي فاتح
                elif balance_value >= 1000:
                    id_icon = "⭐"  # مورد مميز - رصيد جيد
                    color = QColor(59, 130, 246)  # #3b82f6 - أزرق
                elif balance_value > 0:
                    id_icon = "🟢"  # مورد نشط - رصيد موجب
                    color = QColor(22, 163, 74)   # #16a34a - أخضر
                elif balance_value == 0:
                    id_icon = "⚪"  # مورد عادي - رصيد صفر
                    color = QColor(107, 114, 128)  # #6b7280 - رمادي
                else:
                    id_icon = "🔴"  # مورد مدين - رصيد سالب
                    color = QColor(220, 38, 38)   # #dc2626 - أحمر

                id_item = QTableWidgetItem(f"{id_icon} {supplier.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                id_item.setForeground(color)
                self.suppliers_table.setItem(row, 0, id_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 🚛 العمود 1: اسم المورد                                            │
                # └─────────────────────────────────────────────────────────────────────┘
                name_text = supplier.name if supplier.name and supplier.name.strip() else "غير محدد"
                name_item = QTableWidgetItem(f"🚛 {name_text}")
                name_item.setTextAlignment(Qt.AlignCenter)
                name_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if name_text == "غير محدد":
                    name_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 1, name_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 🏭 العمود 2: العنوان                                               │
                # └─────────────────────────────────────────────────────────────────────┘
                address_text = supplier.address if supplier.address and supplier.address.strip() else "غير محدد"
                address_item = QTableWidgetItem(f"🏭 {address_text}")
                address_item.setTextAlignment(Qt.AlignCenter)
                address_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if address_text == "غير محدد":
                    address_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 2, address_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 📧 العمود 3: البريد الإلكتروني                                    │
                # └─────────────────────────────────────────────────────────────────────┘
                email_text = supplier.email if supplier.email and supplier.email.strip() else "غير محدد"
                email_item = QTableWidgetItem(f"📧 {email_text}")
                email_item.setTextAlignment(Qt.AlignCenter)
                email_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if email_text == "غير محدد":
                    email_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 3, email_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 📱 العمود 4: رقم الهاتف                                            │
                # └─────────────────────────────────────────────────────────────────────┘
                phone_text = supplier.phone if supplier.phone and supplier.phone.strip() else "غير محدد"
                phone_item = QTableWidgetItem(f"📱 {phone_text}")
                phone_item.setTextAlignment(Qt.AlignCenter)
                phone_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if phone_text == "غير محدد":
                    phone_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 4, phone_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 💰 العمود 5: الرصيد مع أيقونات متطورة وتلوين حسب المستوى          │
                # └─────────────────────────────────────────────────────────────────────┘
                balance_value = supplier.balance or 0

                # أيقونات متطورة حسب مستوى الرصيد
                if balance_value >= 10000:
                    balance_icon = "💎💰"  # رصيد ملكي
                    color = QColor(147, 51, 234)  # #9333ea - بنفسجي ملكي
                elif balance_value >= 5000:
                    balance_icon = "🏆💰"  # رصيد VIP
                    color = QColor(168, 85, 247)  # #a855f7 - بنفسجي فاتح
                elif balance_value >= 1000:
                    balance_icon = "⭐💰"  # رصيد مميز
                    color = QColor(59, 130, 246)  # #3b82f6 - أزرق
                elif balance_value > 100:
                    balance_icon = "💚💰"  # رصيد جيد
                    color = QColor(34, 197, 94)   # #22c55e - أخضر فاتح
                elif balance_value > 0:
                    balance_icon = "✅💰"  # رصيد موجب
                    color = QColor(22, 163, 74)   # #16a34a - أخضر
                elif balance_value == 0:
                    balance_icon = "⚪💰"  # رصيد صفر
                    color = QColor(107, 114, 128)  # #6b7280 - رمادي
                elif balance_value > -1000:
                    balance_icon = "⚠️💰"  # دين قليل
                    color = QColor(245, 158, 11)  # #f59e0b - برتقالي
                else:
                    balance_icon = "🚨💰"  # دين كبير
                    color = QColor(220, 38, 38)   # #dc2626 - أحمر

                balance_item = QTableWidgetItem(f"{balance_icon} {format_currency(balance_value)}")
                balance_item.setTextAlignment(Qt.AlignCenter)
                balance_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                balance_item.setForeground(color)

                # إضافة خلفية خفيفة للأرصدة المهمة
                if balance_value >= 5000:
                    balance_item.setBackground(QColor(147, 51, 234, 30))  # خلفية بنفسجية خفيفة
                elif balance_value <= -1000:
                    balance_item.setBackground(QColor(220, 38, 38, 30))   # خلفية حمراء خفيفة

                self.suppliers_table.setItem(row, 5, balance_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ ⭐ العمود 6: حالة المورد                                           │
                # └─────────────────────────────────────────────────────────────────────┘
                status = self.get_supplier_status(supplier.balance or 0)
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                self.suppliers_table.setItem(row, 6, status_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 📋 العمود 7: الملاحظات                                             │
                # └─────────────────────────────────────────────────────────────────────┘
                notes_text = supplier.notes if hasattr(supplier, 'notes') and supplier.notes and supplier.notes.strip() else "غير محدد"
                notes_item = QTableWidgetItem(f"📋 {notes_text}")
                notes_item.setTextAlignment(Qt.AlignCenter)
                notes_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if notes_text == "غير محدد":
                    notes_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 7, notes_item)

                # ┌─────────────────────────────────────────────────────────────────────┐
                # │ 🗓️ العمود 8: تاريخ الإضافة                                         │
                # └─────────────────────────────────────────────────────────────────────┘
                if hasattr(supplier, 'created_at') and supplier.created_at:
                    date_text = supplier.created_at.strftime("%Y-%m-%d")
                else:
                    date_text = "غير محدد"
                date_item = QTableWidgetItem(f"🗓️ {date_text}")
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFont(QFont("Segoe UI", 14, QFont.Bold))
                if date_text == "غير محدد":
                    date_item.setForeground(QColor(220, 38, 38))  # #dc2626 - أحمر للبيانات المفقودة
                self.suppliers_table.setItem(row, 8, date_item)

        except Exception as e:
            print(f"خطأ في ملء الجدول: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"

    def create_sample_data_if_empty(self):
        """إنشاء بيانات تجريبية إذا كانت قاعدة البيانات فارغة"""
        try:
            # التحقق من وجود موردين
            existing_suppliers = self.session.query(Supplier).count()

            if existing_suppliers == 0:
                # إنشاء موردين تجريبيين
                sample_suppliers = [
                    Supplier(
                        name='شركة المواد الإنشائية المتحدة',
                        phone='0501234567',
                        email='<EMAIL>',
                        address='الرياض - حي الصناعية - شارع الملك فهد',
                        balance=25000.0,
                        notes='مورد رئيسي - جودة عالية'
                    ),
                    Supplier(
                        name='مؤسسة الدهانات والأصباغ',
                        phone='0509876543',
                        email='<EMAIL>',
                        address='جدة - حي الصفا - طريق الأمير سلطان',
                        balance=12000.0,
                        notes='مورد دهانات موثوق'
                    ),
                    Supplier(
                        name='شركة الأخشاب والنجارة',
                        phone='0551122334',
                        email='<EMAIL>',
                        address='الدمام - حي الفيصلية - شارع الخليج',
                        balance=-5000.0,
                        notes='مورد أخشاب - يحتاج متابعة'
                    )
                ]

                for supplier in sample_suppliers:
                    self.session.add(supplier)

                self.session.commit()
                print(f"تم إنشاء {len(sample_suppliers)} مورد تجريبي")

                # تحديث الجدول
                self.refresh_data()

        except Exception as e:
            print(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة مطابقة للفواتير"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("موردين نشطين", "active"),
            ("موردين عاديين", "normal"),
            ("موردين مدينين", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار مطابق للفواتير"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية مطابق للفواتير"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية مطابق للفواتير"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_suppliers()

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🎯 الوظائف المتطورة المنسوخة من العملاء
    # ═══════════════════════════════════════════════════════════════════════════════

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول - مطابق تماماً للعملاء"""
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            /* تمييز الصف المحدد بألوان متطابقة مع نمط التطبيق */
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.9), stop:0.2 rgba(30, 64, 175, 0.9),
                    stop:0.4 rgba(37, 99, 235, 0.9), stop:0.6 rgba(99, 102, 241, 0.9),
                    stop:0.8 rgba(109, 40, 217, 0.9), stop:1 rgba(76, 29, 149, 0.9)) !important;
                color: #ffffff !important;
                border: 4px solid rgba(255, 255, 255, 0.95) !important;
                border-left: 6px solid #3b82f6 !important;
                border-right: 6px solid #3b82f6 !important;
                border-radius: 18px !important;
                font-weight: 900 !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 0 15px rgba(59, 130, 246, 0.5) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            /* تأثير إضافي للصف المحدد عند التمرير - متطابق مع نمط الألوان */
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.95), stop:0.3 rgba(37, 99, 235, 0.95),
                    stop:0.7 rgba(59, 130, 246, 0.95), stop:1 rgba(96, 165, 250, 0.95)) !important;
                border: 5px solid rgba(255, 255, 255, 0.95) !important;
                border-left: 7px solid #2563eb !important;
                border-right: 7px solid #2563eb !important;
                box-shadow: 0px 10px 25px rgba(59, 130, 246, 0.6) !important;
                transform: translateY(-2px) scale(1.01) !important;
                color: #ffffff !important;
                font-weight: 900 !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول مطابقة للتصميم الموحد"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.suppliers_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.suppliers_table.viewport())
                paint_watermark(painter, self.suppliers_table.viewport().rect())
                painter.end()
            except Exception as e:
                print(f"خطأ في رسم العلامة المائية: {str(e)}")

        self.suppliers_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول"""
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        pass  # يمكن إضافة منطق تحديث الأزرار هنا

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية"""
        try:
            self.quick_edit_supplier(self.suppliers_table.model().index(row, column))
        except Exception as e:
            print(f"خطأ في النقر المزدوج: {str(e)}")

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح المتقدمة"""
        try:
            # Delete للحذف السريع
            delete_shortcut = QShortcut(QKeySequence("Delete"), self)
            delete_shortcut.activated.connect(self.delete_selected_suppliers)

            # F5 للتحديث السريع
            refresh_shortcut = QShortcut(QKeySequence("F5"), self)
            refresh_shortcut.activated.connect(self.refresh_data)

            # Ctrl+F للبحث السريع
            search_shortcut = QShortcut(QKeySequence("Ctrl+F"), self)
            search_shortcut.activated.connect(lambda: self.search_edit.setFocus())

            # Ctrl+A لتحديد جميع الصفوف
            select_all_shortcut = QShortcut(QKeySequence("Ctrl+A"), self)
            select_all_shortcut.activated.connect(self.suppliers_table.selectAll)

            # Escape لإلغاء التحديد
            escape_shortcut = QShortcut(QKeySequence("Escape"), self)
            escape_shortcut.activated.connect(self.suppliers_table.clearSelection)

            print("✅ تم إعداد اختصارات لوحة المفاتيح للموردين بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعداد اختصارات لوحة المفاتيح: {str(e)}")

    def quick_edit_supplier(self, index):
        """تعديل سريع بالنقر المزدوج"""
        try:
            if index.isValid():
                row = index.row()
                supplier_id = self.get_supplier_id_from_row(row)
                if supplier_id:
                    print(f"🖱️ نقر مزدوج على المورد رقم: {supplier_id}")
                    # يمكن إضافة منطق التعديل هنا
                else:
                    QMessageBox.warning(self, "تحذير", "لا يمكن تحديد المورد للتعديل")
        except Exception as e:
            print(f"❌ خطأ في التعديل السريع: {str(e)}")

    def show_context_menu(self, position):
        """عرض قائمة السياق (Right-click menu) مع خيارات سريعة"""
        try:
            if self.suppliers_table.itemAt(position) is None:
                return

            context_menu = QMenu(self)
            context_menu.setStyleSheet("""
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0);
                    border: 3px solid #3B82F6;
                    border-radius: 12px;
                    padding: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
                QMenu::item {
                    padding: 10px 20px;
                    margin: 2px;
                    border-radius: 8px;
                    color: #1f2937;
                    font-weight: 600;
                }
                QMenu::item:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.2), stop:1 rgba(96, 165, 250, 0.3));
                    color: #1e40af;
                    border: 2px solid #3B82F6;
                    font-weight: 900;
                }
            """)

            # إضافة الإجراءات
            edit_action = context_menu.addAction("✏️ تعديل سريع")
            edit_action.triggered.connect(self.quick_edit_supplier)

            delete_action = context_menu.addAction("🗑️ حذف")
            delete_action.triggered.connect(self.delete_selected_suppliers)

            context_menu.addSeparator()

            refresh_action = context_menu.addAction("🔄 تحديث")
            refresh_action.triggered.connect(self.refresh_data)

            context_menu.exec_(self.suppliers_table.mapToGlobal(position))

        except Exception as e:
            print(f"❌ خطأ في عرض قائمة السياق: {str(e)}")

    def delete_selected_suppliers(self):
        """حذف الموردين المحددين (يدعم التحديد المتعدد)"""
        try:
            selected_rows = set()
            for item in self.suppliers_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مورد أو أكثر للحذف")
                return

            count = len(selected_rows)
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف {count} مورد؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                deleted_count = 0
                for row in sorted(selected_rows, reverse=True):
                    supplier_id = self.get_supplier_id_from_row(row)
                    if supplier_id:
                        supplier = self.session.query(Supplier).get(supplier_id)
                        if supplier:
                            self.session.delete(supplier)
                            deleted_count += 1

                self.session.commit()
                self.refresh_data()
                QMessageBox.information(self, "نجح", f"✅ تم حذف {deleted_count} مورد بنجاح")

        except Exception as e:
            self.session.rollback()
            print(f"❌ خطأ في حذف الموردين: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الموردين: {str(e)}")

    def get_supplier_id_from_row(self, row):
        """الحصول على معرف المورد من رقم الصف"""
        try:
            id_item = self.suppliers_table.item(row, 0)
            if id_item:
                # استخراج الرقم من النص (إزالة الأيقونة)
                id_text = id_item.text().split()[-1]  # آخر كلمة هي الرقم
                return int(id_text)
        except:
            pass
        return None
